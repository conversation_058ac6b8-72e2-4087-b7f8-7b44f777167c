<script setup lang="ts">
import { ref, computed } from 'vue';

// Define emits for parent communication
const emit = defineEmits<{
  'create-successful': []
}>();

// Form data
const username = ref('');
const password = ref('');

// Error states
const apiError = ref('');
const isSubmitting = ref(false);

// Password validation criteria
const passwordCriteria = computed(() => {
  const criteria = [];

  if (password.value.length < 10) {
    criteria.push('Password must be at least 10 characters long');
  }

  if (password.value.length > 24) {
    criteria.push('Password must be at most 24 characters long');
  }

  if (password.value.includes(' ')) {
    criteria.push('Password cannot contain spaces');
  }

  if (!/\d/.test(password.value)) {
    criteria.push('Password must contain at least one number');
  }

  if (!/[A-Z]/.test(password.value)) {
    criteria.push('Password must contain at least one uppercase letter');
  }

  if (!/[a-z]/.test(password.value)) {
    criteria.push('Password must contain at least one lowercase letter');
  }

  return criteria;
});

// Check if form is valid for submission
const isFormValid = computed(() => {
  return username.value.trim() !== '' && passwordCriteria.value.length === 0;
});

// Handle form submission
const handleSubmit = async (event: Event) => {
  event.preventDefault();

  // Reset previous API errors
  apiError.value = '';

  // Don't submit if form is invalid
  if (!isFormValid.value) {
    return;
  }

  isSubmitting.value = true;

  try {
    const response = await fetch('https://api.challenge.hennge.com/password-validation-challenge-api/001/challenge-signup', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************._zywRVzPMBQdfPNIfbXtkp-N4dbzbc0ZArwXnAkZux4'
      },
      body: JSON.stringify({
        username: username.value,
        password: password.value
      })
    });

    if (response.ok) {
      // Success - emit event to parent
      emit('create-successful');
    } else if (response.status === 401 || response.status === 403) {
      // Unauthorized/Forbidden
      apiError.value = 'Not authenticated to access this resource.';
    } else if (response.status === 422) {
      // Validation error from server
      const errorData = await response.json();
      if (errorData.errors && errorData.errors.includes('not_allowed')) {
        apiError.value = 'Sorry, the entered password is not allowed, please try a different one.';
      } else {
        apiError.value = 'Something went wrong, please try again.';
      }
    } else if (response.status === 500) {
      // Server error
      apiError.value = 'Something went wrong, please try again.';
    } else {
      // Generic error
      apiError.value = 'Something went wrong, please try again.';
    }
  } catch (error) {
    // Network or other errors
    apiError.value = 'Something went wrong, please try again.';
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<template>
  <div class="form-wrapper">
    <form class="form" @submit="handleSubmit">
      <!-- API Error Message -->
      <div v-if="apiError" class="error-message api-error">
        {{ apiError }}
      </div>

      <!-- Username Field -->
      <label for="username">Username</label>
      <input
        id="username"
        v-model="username"
        type="text"
        :aria-invalid="username.trim() === '' ? 'true' : 'false'"
      />

      <!-- Password Field -->
      <label for="password">Password</label>
      <input
        id="password"
        v-model="password"
        type="password"
        :aria-invalid="passwordCriteria.length > 0 ? 'true' : 'false'"
      />

      <!-- Password Validation Criteria -->
      <div v-if="passwordCriteria.length > 0" class="validation-errors">
        <ul>
          <li v-for="criterion in passwordCriteria" :key="criterion">
            {{ criterion }}
          </li>
        </ul>
      </div>

      <button
        type="submit"
        class="submit-button"
        :disabled="!isFormValid || isSubmitting"
      >
        {{ isSubmitting ? 'Creating...' : 'Create User' }}
      </button>
    </form>
  </div>
</template>

<style scoped>
.form-wrapper {
  max-width: 500px;
  width: 80%;
  background-color: #efeef5;
  padding: 24px;
  margin: auto;
  border-radius: 8px;
}

.form {
  display: flex;
  gap: 8px;
  flex-direction: column;
}

label {
  font-weight: 700;
}

input {
  outline: none;
  padding: 8px 16px;
  height: 40px;
  font-size: 14px;
  background-color: #f8f7fa;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
}

.submit-button {
  outline: none;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  background-color: #7135d2;
  color: white;
  font-size: 16px;
  font-weight: 500;
  height: 40px;
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 8px;
  align-self: flex-end;
  cursor: pointer;
}

.submit-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.error-message {
  color: #d32f2f;
  font-size: 14px;
  margin-bottom: 8px;
}

.api-error {
  background-color: #ffebee;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 4px solid #d32f2f;
}

.validation-errors {
  margin-top: 4px;
}

.validation-errors ul {
  margin: 0;
  padding-left: 20px;
  color: #d32f2f;
  font-size: 14px;
}

.validation-errors li {
  margin-bottom: 4px;
}
</style>
